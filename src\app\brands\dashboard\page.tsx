import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  BarChart3, 
  Users, 
  TrendingUp, 
  DollarSign, 
  Plus,
  Eye,
  MessageSquare,
  Calendar
} from "lucide-react";

export default function BrandDashboard() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">Dobrodošli nazad! Evo pregleda vaših kampanja.</p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Nova Kampanja
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktivne Kampanje</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              +2 od prošlog mjeseca
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ukupni Doseg</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.4M</div>
            <p className="text-xs text-muted-foreground">
              +15% od prošlog mjeseca
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.2%</div>
            <p className="text-xs text-muted-foreground">
              +0.5% od prošlog mjeseca
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ukupno Potrošeno</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">€8,450</div>
            <p className="text-xs text-muted-foreground">
              +12% od prošlog mjeseca
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Campaigns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Nedavne Kampanje</CardTitle>
            <CardDescription>
              Pregled vaših najnovijih kampanja
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  name: "Ljetnja Kolekcija 2024",
                  status: "Aktivna",
                  influencers: 8,
                  budget: "€2,500",
                  engagement: "3.8%"
                },
                {
                  name: "Black Friday Promocija",
                  status: "Završena",
                  influencers: 15,
                  budget: "€5,000",
                  engagement: "5.2%"
                },
                {
                  name: "Proljetni Lookbook",
                  status: "U pripremi",
                  influencers: 6,
                  budget: "€1,800",
                  engagement: "-"
                }
              ].map((campaign, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium">{campaign.name}</h4>
                    <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {campaign.influencers} influencera
                      </span>
                      <span className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        {campaign.budget}
                      </span>
                      <span className="flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        {campaign.engagement}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={
                        campaign.status === "Aktivna" ? "default" : 
                        campaign.status === "Završena" ? "secondary" : 
                        "outline"
                      }
                    >
                      {campaign.status}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Nedavne Aktivnosti</CardTitle>
            <CardDescription>
              Najnovije aktivnosti na vašem računu
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  action: "Nova aplikacija",
                  description: "@marija_style aplicirala za 'Ljetnja Kolekcija 2024'",
                  time: "prije 2 sata",
                  icon: MessageSquare
                },
                {
                  action: "Kampanja završena",
                  description: "'Black Friday Promocija' je uspješno završena",
                  time: "prije 1 dan",
                  icon: Calendar
                },
                {
                  action: "Novi sadržaj",
                  description: "@stefan_fitness objavio post za 'Fitness Challenge'",
                  time: "prije 2 dana",
                  icon: Eye
                },
                {
                  action: "Plaćanje obrađeno",
                  description: "Plaćanje od €500 uspješno obrađeno",
                  time: "prije 3 dana",
                  icon: DollarSign
                }
              ].map((activity, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <activity.icon className="h-4 w-4 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{activity.action}</h4>
                    <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
