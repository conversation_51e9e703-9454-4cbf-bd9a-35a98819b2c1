import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, 
  TrendingUp, 
  Users, 
  Calendar,
  Eye,
  Heart,
  MessageSquare,
  Star,
  Clock,
  CheckCircle
} from "lucide-react";

export default function InfluencerDashboard() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Moj Dashboard</h1>
          <p className="text-gray-600 mt-2">Dobrodo<PERSON><PERSON> nazad! Evo pregleda vaših aktivnosti.</p>
        </div>
        <Button className="flex items-center gap-2">
          <Eye className="h-4 w-4" />
          Pogledaj Profil
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ukupna Zarada</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">€3,240</div>
            <p className="text-xs text-muted-foreground">
              +€520 ovaj mjesec
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktivne Kampanje</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">
              +2 nova ova sedmica
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ukupni Doseg</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">125K</div>
            <p className="text-xs text-muted-foreground">
              +8% od prošlog mjeseca
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Engagement</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.2%</div>
            <p className="text-xs text-muted-foreground">
              +0.3% od prošlog mjeseca
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Active Campaigns */}
        <Card>
          <CardHeader>
            <CardTitle>Aktivne Kampanje</CardTitle>
            <CardDescription>
              Kampanje na kojima trenutno radite
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  brand: "Fashion Nova BA",
                  campaign: "Ljetnja Kolekcija 2024",
                  type: "Instagram Post + Story",
                  deadline: "15. Jul 2024",
                  payment: "€200",
                  status: "U toku",
                  progress: 60
                },
                {
                  brand: "TechStore BiH",
                  campaign: "Smartphone Review",
                  type: "YouTube Video",
                  deadline: "20. Jul 2024",
                  payment: "€350",
                  status: "Čeka odobrenje",
                  progress: 90
                },
                {
                  brand: "Fitness Center Sarajevo",
                  campaign: "Workout Challenge",
                  type: "TikTok Video",
                  deadline: "25. Jul 2024",
                  payment: "€150",
                  status: "Planiranje",
                  progress: 20
                }
              ].map((campaign, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{campaign.campaign}</h4>
                      <p className="text-sm text-gray-600">{campaign.brand}</p>
                    </div>
                    <Badge 
                      variant={
                        campaign.status === "U toku" ? "default" : 
                        campaign.status === "Čeka odobrenje" ? "secondary" : 
                        "outline"
                      }
                    >
                      {campaign.status}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Tip:</span>
                      <span className="ml-1 font-medium">{campaign.type}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Plaćanje:</span>
                      <span className="ml-1 font-medium text-green-600">{campaign.payment}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1 text-gray-600">
                      <Clock className="h-3 w-3" />
                      <span>Rok: {campaign.deadline}</span>
                    </div>
                    <span className="text-gray-600">{campaign.progress}% završeno</span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all" 
                      style={{ width: `${campaign.progress}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Nedavne Performanse</CardTitle>
            <CardDescription>
              Rezultati vaših najnovijih objava
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  campaign: "Beauty Brand Collab",
                  platform: "Instagram",
                  type: "Post",
                  date: "12. Jul 2024",
                  views: "15.2K",
                  likes: "1.8K",
                  comments: "124",
                  engagement: "12.6%"
                },
                {
                  campaign: "Summer Vibes",
                  platform: "TikTok",
                  type: "Video",
                  date: "10. Jul 2024",
                  views: "45.8K",
                  likes: "3.2K",
                  comments: "89",
                  engagement: "7.2%"
                },
                {
                  campaign: "Tech Review",
                  platform: "YouTube",
                  type: "Video",
                  date: "8. Jul 2024",
                  views: "8.9K",
                  likes: "567",
                  comments: "45",
                  engagement: "6.9%"
                }
              ].map((post, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">{post.campaign}</h4>
                      <p className="text-sm text-gray-600">{post.platform} • {post.type} • {post.date}</p>
                    </div>
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      {post.engagement}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Eye className="h-3 w-3 text-gray-500" />
                      <span className="text-gray-600">Pregledi:</span>
                      <span className="font-medium">{post.views}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Heart className="h-3 w-3 text-gray-500" />
                      <span className="text-gray-600">Lajkovi:</span>
                      <span className="font-medium">{post.likes}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-3 w-3 text-gray-500" />
                      <span className="text-gray-600">Komentari:</span>
                      <span className="font-medium">{post.comments}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Brze Akcije</CardTitle>
          <CardDescription>
            Često korišćene funkcije
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <Star className="h-6 w-6" />
              <span>Ažuriraj Cijene</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <Eye className="h-6 w-6" />
              <span>Uredi Profil</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <CheckCircle className="h-6 w-6" />
              <span>Završi Kampanju</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
