"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { 
  Camera,
  Instagram,
  Youtube,
  Music,
  MapPin,
  Star,
  Save,
  Plus,
  X
} from "lucide-react";

export default function InfluencerProfile() {
  const [profileData, setProfileData] = useState({
    name: "<PERSON><PERSON>",
    username: "marija_style",
    bio: "Fashion & lifestyle influencer iz Sarajeva. Vol<PERSON> da dijelim svoje outfite i lifestyle savjete sa svojom zajednicom.",
    location: "Sarajevo, BiH",
    category: "fashion",
    instagramHandle: "@marija_style",
    instagramFollowers: "125000",
    tiktokHandle: "@marija_style",
    tiktokFollowers: "89000",
    youtubeHandle: "",
    youtubeFollowers: "",
    priceInstagramPost: "150",
    priceInstagramStory: "75",
    priceInstagramReel: "200",
    priceTikTokVideo: "180",
    priceYouTubeVideo: "",
    priceYouTubeShort: ""
  });

  const [tags, setTags] = useState(["fashion", "lifestyle", "beauty", "sarajevo"]);
  const [newTag, setNewTag] = useState("");

  const handleInputChange = (field: string, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Moj Profil</h1>
        <p className="text-gray-600">Uredite svoj profil i postavite cijene za različite tipove sadržaja</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Profile Preview */}
        <div className="lg:col-span-1">
          <Card className="sticky top-8">
            <CardHeader>
              <CardTitle>Pregled Profila</CardTitle>
              <CardDescription>Ovako vas vide brendovi</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col items-center text-center">
                <div className="relative">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src="/avatars/marija.jpg" alt={profileData.name} />
                    <AvatarFallback>{profileData.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <Button size="icon" variant="outline" className="absolute -bottom-2 -right-2 h-8 w-8">
                    <Camera className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="mt-4">
                  <h3 className="font-semibold text-lg">{profileData.name}</h3>
                  <p className="text-gray-600">@{profileData.username}</p>
                  <div className="flex items-center justify-center gap-1 mt-2">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">4.8</span>
                    <Badge variant="secondary" className="ml-2 text-xs">✓ Verifikovan</Badge>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-gray-700">{profileData.bio}</p>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{profileData.location}</span>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">Platforme:</h4>
                <div className="space-y-1">
                  {profileData.instagramHandle && (
                    <div className="flex items-center gap-2 text-sm">
                      <Instagram className="h-4 w-4" />
                      <span>{profileData.instagramFollowers} pratilaca</span>
                    </div>
                  )}
                  {profileData.tiktokHandle && (
                    <div className="flex items-center gap-2 text-sm">
                      <Music className="h-4 w-4" />
                      <span>{profileData.tiktokFollowers} pratilaca</span>
                    </div>
                  )}
                  {profileData.youtubeHandle && (
                    <div className="flex items-center gap-2 text-sm">
                      <Youtube className="h-4 w-4" />
                      <span>{profileData.youtubeFollowers} pretplatnika</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">Tagovi:</h4>
                <div className="flex flex-wrap gap-1">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Profile Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle>Osnovne Informacije</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Ime i Prezime</Label>
                  <Input
                    id="name"
                    value={profileData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="username">Korisničko ime</Label>
                  <Input
                    id="username"
                    value={profileData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">Biografija</Label>
                <Textarea
                  id="bio"
                  value={profileData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location">Lokacija</Label>
                  <Input
                    id="location"
                    value={profileData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Kategorija</Label>
                  <Select value={profileData.category} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fashion">Fashion & Lifestyle</SelectItem>
                      <SelectItem value="beauty">Beauty & Skincare</SelectItem>
                      <SelectItem value="fitness">Fitness & Health</SelectItem>
                      <SelectItem value="food">Food & Travel</SelectItem>
                      <SelectItem value="tech">Technology</SelectItem>
                      <SelectItem value="gaming">Gaming</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tagovi</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="flex items-center gap-1">
                      {tag}
                      <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    placeholder="Dodaj tag"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                  />
                  <Button type="button" variant="outline" onClick={addTag}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Social Media */}
          <Card>
            <CardHeader>
              <CardTitle>Društvene Mreže</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="instagram">Instagram Handle</Label>
                  <Input
                    id="instagram"
                    placeholder="@username"
                    value={profileData.instagramHandle}
                    onChange={(e) => handleInputChange('instagramHandle', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="instagramFollowers">Instagram Pratilaca</Label>
                  <Input
                    id="instagramFollowers"
                    type="number"
                    value={profileData.instagramFollowers}
                    onChange={(e) => handleInputChange('instagramFollowers', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tiktok">TikTok Handle</Label>
                  <Input
                    id="tiktok"
                    placeholder="@username"
                    value={profileData.tiktokHandle}
                    onChange={(e) => handleInputChange('tiktokHandle', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tiktokFollowers">TikTok Pratilaca</Label>
                  <Input
                    id="tiktokFollowers"
                    type="number"
                    value={profileData.tiktokFollowers}
                    onChange={(e) => handleInputChange('tiktokFollowers', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="youtube">YouTube Kanal</Label>
                  <Input
                    id="youtube"
                    placeholder="Naziv kanala"
                    value={profileData.youtubeHandle}
                    onChange={(e) => handleInputChange('youtubeHandle', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="youtubeFollowers">YouTube Pretplatnika</Label>
                  <Input
                    id="youtubeFollowers"
                    type="number"
                    value={profileData.youtubeFollowers}
                    onChange={(e) => handleInputChange('youtubeFollowers', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle>Cijene Sadržaja</CardTitle>
              <CardDescription>Postavite cijene za različite tipove sadržaja (u EUR)</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priceInstagramPost">Instagram Post</Label>
                  <Input
                    id="priceInstagramPost"
                    type="number"
                    placeholder="0"
                    value={profileData.priceInstagramPost}
                    onChange={(e) => handleInputChange('priceInstagramPost', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priceInstagramStory">Instagram Story</Label>
                  <Input
                    id="priceInstagramStory"
                    type="number"
                    placeholder="0"
                    value={profileData.priceInstagramStory}
                    onChange={(e) => handleInputChange('priceInstagramStory', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priceInstagramReel">Instagram Reel</Label>
                  <Input
                    id="priceInstagramReel"
                    type="number"
                    placeholder="0"
                    value={profileData.priceInstagramReel}
                    onChange={(e) => handleInputChange('priceInstagramReel', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priceTikTokVideo">TikTok Video</Label>
                  <Input
                    id="priceTikTokVideo"
                    type="number"
                    placeholder="0"
                    value={profileData.priceTikTokVideo}
                    onChange={(e) => handleInputChange('priceTikTokVideo', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priceYouTubeVideo">YouTube Video</Label>
                  <Input
                    id="priceYouTubeVideo"
                    type="number"
                    placeholder="0"
                    value={profileData.priceYouTubeVideo}
                    onChange={(e) => handleInputChange('priceYouTubeVideo', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priceYouTubeShort">YouTube Short</Label>
                  <Input
                    id="priceYouTubeShort"
                    type="number"
                    placeholder="0"
                    value={profileData.priceYouTubeShort}
                    onChange={(e) => handleInputChange('priceYouTubeShort', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              Sačuvaj Promjene
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
