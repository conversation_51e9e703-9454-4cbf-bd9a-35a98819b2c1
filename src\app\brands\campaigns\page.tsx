"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Plus,
  Search,
  Filter,
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  Eye,
  Edit,
  MoreHorizontal,
  Play,
  Pause,
  Archive
} from "lucide-react";

const mockCampaigns = [
  {
    id: 1,
    name: "<PERSON>jet<PERSON> Kolek<PERSON>ja 2024",
    description: "Promocija nove ljetnje kolekcije odjeće",
    status: "Aktivna",
    budget: "€2,500",
    spent: "€1,200",
    influencers: 8,
    applications: 15,
    startDate: "1. Jul 2024",
    endDate: "31. Jul 2024",
    reach: "245K",
    engagement: "3.8%",
    category: "Fashion"
  },
  {
    id: 2,
    name: "Black Friday Promocija",
    description: "Velika Black Friday rasprodaja",
    status: "Završena",
    budget: "€5,000",
    spent: "€4,850",
    influencers: 15,
    applications: 32,
    startDate: "20. Nov 2023",
    endDate: "30. Nov 2023",
    reach: "580K",
    engagement: "5.2%",
    category: "E-commerce"
  },
  {
    id: 3,
    name: "Proljetni Lookbook",
    description: "Predstavljanje proljetnih trendova",
    status: "U pripremi",
    budget: "€1,800",
    spent: "€0",
    influencers: 0,
    applications: 6,
    startDate: "15. Mar 2024",
    endDate: "15. Apr 2024",
    reach: "-",
    engagement: "-",
    category: "Fashion"
  }
];

export default function CampaignsPage() {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const [newCampaign, setNewCampaign] = useState({
    name: "",
    description: "",
    budget: "",
    category: "",
    startDate: "",
    endDate: "",
    requirements: ""
  });

  const handleCreateCampaign = () => {
    // Ovdje bi se poslao API poziv za kreiranje kampanje
    console.log("Creating campaign:", newCampaign);
    setShowCreateForm(false);
    setNewCampaign({
      name: "",
      description: "",
      budget: "",
      category: "",
      startDate: "",
      endDate: "",
      requirements: ""
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Aktivna":
        return "default";
      case "Završena":
        return "secondary";
      case "U pripremi":
        return "outline";
      default:
        return "outline";
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Kampanje</h1>
          <p className="text-gray-600 mt-2">Upravljajte svojim influencer marketing kampanjama</p>
        </div>
        <Button onClick={() => setShowCreateForm(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Nova Kampanja
        </Button>
      </div>

      {/* Create Campaign Form */}
      {showCreateForm && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Kreiraj Novu Kampanju</CardTitle>
            <CardDescription>Unesite detalje za vašu novu kampanju</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="campaignName">Naziv Kampanje</Label>
                <Input
                  id="campaignName"
                  placeholder="Unesite naziv kampanje"
                  value={newCampaign.name}
                  onChange={(e) => setNewCampaign({...newCampaign, name: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="budget">Budžet (EUR)</Label>
                <Input
                  id="budget"
                  type="number"
                  placeholder="0"
                  value={newCampaign.budget}
                  onChange={(e) => setNewCampaign({...newCampaign, budget: e.target.value})}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Opis Kampanje</Label>
              <Textarea
                id="description"
                placeholder="Opišite vašu kampanju..."
                value={newCampaign.description}
                onChange={(e) => setNewCampaign({...newCampaign, description: e.target.value})}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Kategorija</Label>
                <Select value={newCampaign.category} onValueChange={(value) => setNewCampaign({...newCampaign, category: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Odaberite kategoriju" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fashion">Fashion & Lifestyle</SelectItem>
                    <SelectItem value="beauty">Beauty & Skincare</SelectItem>
                    <SelectItem value="fitness">Fitness & Health</SelectItem>
                    <SelectItem value="food">Food & Travel</SelectItem>
                    <SelectItem value="tech">Technology</SelectItem>
                    <SelectItem value="ecommerce">E-commerce</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="startDate">Datum Početka</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={newCampaign.startDate}
                  onChange={(e) => setNewCampaign({...newCampaign, startDate: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">Datum Završetka</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={newCampaign.endDate}
                  onChange={(e) => setNewCampaign({...newCampaign, endDate: e.target.value})}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="requirements">Zahtjevi za Influencere</Label>
              <Textarea
                id="requirements"
                placeholder="Specificirajte zahtjeve za influencere (min. broj pratilaca, tip sadržaja, itd.)"
                value={newCampaign.requirements}
                onChange={(e) => setNewCampaign({...newCampaign, requirements: e.target.value})}
                rows={3}
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button onClick={handleCreateCampaign}>Kreiraj Kampanju</Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>Otkaži</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filters */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filteri
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Pretraži Kampanje</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Naziv kampanje"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Svi statusi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Svi statusi</SelectItem>
                  <SelectItem value="aktivna">Aktivna</SelectItem>
                  <SelectItem value="zavrsena">Završena</SelectItem>
                  <SelectItem value="priprema">U pripremi</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Sortiranje</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Najnovije" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Najnovije</SelectItem>
                  <SelectItem value="oldest">Najstarije</SelectItem>
                  <SelectItem value="budget-high">Najveći budžet</SelectItem>
                  <SelectItem value="budget-low">Najmanji budžet</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Campaigns List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {mockCampaigns.map((campaign) => (
          <Card key={campaign.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{campaign.name}</CardTitle>
                  <CardDescription className="mt-1">{campaign.description}</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusColor(campaign.status)}>
                    {campaign.status}
                  </Badge>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">Period:</span>
                  <span className="font-medium">{campaign.startDate} - {campaign.endDate}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">Influenceri:</span>
                  <span className="font-medium">{campaign.influencers}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">Budžet:</span>
                  <span className="font-medium">{campaign.budget}</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">Potrošeno:</span>
                  <span className="font-medium">{campaign.spent}</span>
                </div>
              </div>

              {campaign.reach !== "-" && (
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">Doseg:</span>
                    <span className="font-medium">{campaign.reach}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">Engagement:</span>
                    <span className="font-medium">{campaign.engagement}</span>
                  </div>
                </div>
              )}

              <div className="flex gap-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Eye className="h-4 w-4 mr-1" />
                  Detalji
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Edit className="h-4 w-4 mr-1" />
                  Uredi
                </Button>
                {campaign.status === "Aktivna" && (
                  <Button variant="outline" size="sm">
                    <Pause className="h-4 w-4" />
                  </Button>
                )}
                {campaign.status === "U pripremi" && (
                  <Button variant="outline" size="sm">
                    <Play className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
