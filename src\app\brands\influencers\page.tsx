"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Search, 
  Filter, 
  Users, 
  Heart, 
  MessageCircle,
  Instagram,
  Youtube,
  Music,
  MapPin,
  Star,
  Send
} from "lucide-react";

const mockInfluencers = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    username: "@marija_style",
    avatar: "/avatars/marija.jpg",
    followers: "125K",
    engagement: "4.2%",
    category: "Fashion & Lifestyle",
    location: "Sarajevo, BiH",
    platforms: ["instagram", "tiktok"],
    rating: 4.8,
    priceInstagramPost: "€150",
    priceInstagramStory: "€75",
    priceTikTokVideo: "€200",
    verified: true
  },
  {
    id: 2,
    name: "Stefan Nikolić",
    username: "@stefan_fitness",
    avatar: "/avatars/stefan.jpg",
    followers: "89K",
    engagement: "5.1%",
    category: "Fitness & Health",
    location: "Banja Luka, BiH",
    platforms: ["instagram", "youtube"],
    rating: 4.9,
    priceInstagramPost: "€120",
    priceInstagramStory: "€60",
    priceYouTubeVideo: "€300",
    verified: true
  },
  {
    id: 3,
    name: "Ana Jovanović",
    username: "@ana_beauty",
    avatar: "/avatars/ana.jpg",
    followers: "67K",
    engagement: "3.8%",
    category: "Beauty & Skincare",
    location: "Mostar, BiH",
    platforms: ["instagram", "tiktok"],
    rating: 4.6,
    priceInstagramPost: "€100",
    priceInstagramStory: "€50",
    priceTikTokVideo: "€150",
    verified: false
  },
  {
    id: 4,
    name: "Miloš Đorđević",
    username: "@milos_tech",
    avatar: "/avatars/milos.jpg",
    followers: "45K",
    engagement: "6.2%",
    category: "Technology",
    location: "Tuzla, BiH",
    platforms: ["youtube", "instagram"],
    rating: 4.7,
    priceInstagramPost: "€80",
    priceInstagramStory: "€40",
    priceYouTubeVideo: "€250",
    verified: true
  }
];

export default function InfluencersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("");
  const [minFollowers, setMinFollowers] = useState("");

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "instagram":
        return <Instagram className="h-4 w-4" />;
      case "youtube":
        return <Youtube className="h-4 w-4" />;
      case "tiktok":
        return <Music className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Pronađi Influencere</h1>
        <p className="text-gray-600">Pretražite i kontaktirajte najbolje influencere za vašu kampanju</p>
      </div>

      {/* Search and Filters */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filteri
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Pretraži</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Ime ili @username"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Kategorija</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Sve kategorije" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Sve kategorije</SelectItem>
                  <SelectItem value="fashion">Fashion & Lifestyle</SelectItem>
                  <SelectItem value="beauty">Beauty & Skincare</SelectItem>
                  <SelectItem value="fitness">Fitness & Health</SelectItem>
                  <SelectItem value="food">Food & Travel</SelectItem>
                  <SelectItem value="tech">Technology</SelectItem>
                  <SelectItem value="gaming">Gaming</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Lokacija</Label>
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger>
                  <SelectValue placeholder="Sve lokacije" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Sve lokacije</SelectItem>
                  <SelectItem value="sarajevo">Sarajevo</SelectItem>
                  <SelectItem value="banja-luka">Banja Luka</SelectItem>
                  <SelectItem value="mostar">Mostar</SelectItem>
                  <SelectItem value="tuzla">Tuzla</SelectItem>
                  <SelectItem value="zenica">Zenica</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Min. Pratilaca</Label>
              <Select value={minFollowers} onValueChange={setMinFollowers}>
                <SelectTrigger>
                  <SelectValue placeholder="Bilo koji broj" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Bilo koji broj</SelectItem>
                  <SelectItem value="1000">1K+</SelectItem>
                  <SelectItem value="10000">10K+</SelectItem>
                  <SelectItem value="50000">50K+</SelectItem>
                  <SelectItem value="100000">100K+</SelectItem>
                  <SelectItem value="500000">500K+</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockInfluencers.map((influencer) => (
          <Card key={influencer.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={influencer.avatar} alt={influencer.name} />
                    <AvatarFallback>{influencer.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{influencer.name}</h3>
                      {influencer.verified && (
                        <Badge variant="secondary" className="text-xs">
                          ✓ Verifikovan
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{influencer.username}</p>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{influencer.rating}</span>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1 text-gray-600">
                  <Users className="h-4 w-4" />
                  <span>{influencer.followers} pratilaca</span>
                </div>
                <div className="flex items-center gap-1 text-gray-600">
                  <Heart className="h-4 w-4" />
                  <span>{influencer.engagement} engagement</span>
                </div>
              </div>

              <div className="flex items-center gap-1 text-sm text-gray-600">
                <MapPin className="h-4 w-4" />
                <span>{influencer.location}</span>
              </div>

              <Badge variant="outline">{influencer.category}</Badge>

              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Platforme:</span>
                <div className="flex gap-1">
                  {influencer.platforms.map((platform) => (
                    <div key={platform} className="p-1 bg-gray-100 rounded">
                      {getPlatformIcon(platform)}
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2 pt-2 border-t">
                <h4 className="text-sm font-medium">Cijene:</h4>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="text-gray-600">IG Post:</span>
                    <span className="font-medium ml-1">{influencer.priceInstagramPost}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">IG Story:</span>
                    <span className="font-medium ml-1">{influencer.priceInstagramStory}</span>
                  </div>
                  {influencer.priceTikTokVideo && (
                    <div>
                      <span className="text-gray-600">TikTok:</span>
                      <span className="font-medium ml-1">{influencer.priceTikTokVideo}</span>
                    </div>
                  )}
                  {influencer.priceYouTubeVideo && (
                    <div>
                      <span className="text-gray-600">YouTube:</span>
                      <span className="font-medium ml-1">{influencer.priceYouTubeVideo}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  Pogledaj Profil
                </Button>
                <Button size="sm" className="flex-1">
                  <Send className="h-4 w-4 mr-1" />
                  Kontaktiraj
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
