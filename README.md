# Collabstr BA - Influencer Marketing Platform

Prva platforma za influencer marketing u Bosni i Hercegovini. Povezujemo brendove sa najtalentovanijim kreatorima sadržaja.

## 🚀 Tehnologije

- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS + Shadcn/ui
- **Backend**: Supabase (PostgreSQL + Auth + Real-time + Storage)
- **Styling**: Tailwind CSS sa custom tamno plavom temom
- **Komponente**: Shadcn/ui za moderne UI komponente
- **Ikone**: Lucide React

## 🎯 Funkcionalnosti

### Za Brendove
- **Dashboard** - Pregled kampanja, statistika i aktivnosti
- **Pretraživanje Influencera** - Napredni filteri za pronalaženje idealnih influencera
- **Upravljanje Kampanjama** - Kreiranje, uređivanje i praćenje kampanja
- **Analytics** - Detaljni izvještaji o performansama

### Za Influencere
- **Dashboard** - Pregled zarada, aktivnih kampanja i performansi
- **Profil** - Uređivanje profila i postavljanje cijena za različite tipove sadržaja
- **Prilike** - Pregled dostupnih kampanja i aplikacija
- **Zarade** - Praćenje prihoda i plaćanja

## 🛠️ Pokretanje Projekta

1. **Kloniraj repozitorij**
```bash
git clone https://github.com/matrixbih/2-Collabstr-Clone.git
cd "2 Collabstr Clone"
```

2. **Instaliraj dependencies**
```bash
npm install
```

3. **Pokreni development server**
```bash
npm run dev
```

4. **Otvori u browseru**
Idi na [http://localhost:3000](http://localhost:3000)

## 📁 Struktura Projekta

```
src/
├── app/                    # Next.js App Router
│   ├── brands/            # Stranice za brendove
│   │   ├── dashboard/     # Dashboard za brendove
│   │   ├── influencers/   # Pretraživanje influencera
│   │   └── campaigns/     # Upravljanje kampanjama
│   ├── influencers/       # Stranice za influencere
│   │   ├── dashboard/     # Dashboard za influencere
│   │   └── profile/       # Profil i postavke cijena
│   ├── globals.css        # Globalni stilovi
│   ├── layout.tsx         # Root layout
│   └── page.tsx          # Početna stranica
├── components/            # React komponente
│   ├── ui/               # Shadcn/ui komponente
│   ├── navigation.tsx    # Glavna navigacija
│   └── footer.tsx        # Footer komponenta
└── lib/                  # Utility funkcije
    └── utils.ts          # Helper funkcije
```

## 🎨 Design System

Platforma koristi tamno plavu (#1e3a8a) kao primarnu boju sa modernim, profesionalnim dizajnom:

- **Primarna boja**: Tamno plava (oklch(0.25 0.15 250))
- **Tipografija**: Geist Sans font family
- **Komponente**: Shadcn/ui sa custom styling
- **Responsive**: Mobile-first pristup

## 🔧 Sledeći Koraci

1. **Supabase Setup** - Konfiguracija baze podataka i autentifikacije
2. **API Integration** - Povezivanje sa backend servisima
3. **Payment Integration** - Implementacija sistema plaćanja
4. **Real-time Features** - Chat i notifikacije
5. **Analytics Dashboard** - Detaljni izvještaji i grafici

## 📝 Napomene

- Svi fajlovi i kod su na engleskom jeziku
- UI tekst je na bosanskom jeziku
- Platforma je optimizovana za bosanski/hrvatski/srpski tržište
- Fokus na jednostavnost i profesionalnost

## 🤝 Doprinos

Ovaj projekat je u razvoju. Za pitanja ili sugestije, kontaktirajte tim.

## 📄 Licenca

Sva prava zadržana © 2024 Collabstr BA
