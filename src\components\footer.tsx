import Link from "next/link";
import { Instagram, Youtube, Music, Mail, Phone, MapPin } from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <span className="font-bold text-xl">Collabstr BA</span>
            </div>
            <p className="text-gray-300 text-sm">
              Prva platforma za influencer marketing u Bosni i Hercegovini. 
              Povezujemo brendove sa najtalentovanijim kreatorima sadržaja.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-gray-300 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-300 hover:text-white transition-colors">
                <Youtube className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-300 hover:text-white transition-colors">
                <Music className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* For Brands */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Za Brendove</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/brands/dashboard" className="text-gray-300 hover:text-white transition-colors">
                  Dashboard
                </Link>
              </li>
              <li>
                <Link href="/brands/influencers" className="text-gray-300 hover:text-white transition-colors">
                  Pronađi Influencere
                </Link>
              </li>
              <li>
                <Link href="/brands/campaigns" className="text-gray-300 hover:text-white transition-colors">
                  Kampanje
                </Link>
              </li>
              <li>
                <Link href="/brands/analytics" className="text-gray-300 hover:text-white transition-colors">
                  Analytics
                </Link>
              </li>
              <li>
                <Link href="/brands/pricing" className="text-gray-300 hover:text-white transition-colors">
                  Cijene
                </Link>
              </li>
            </ul>
          </div>

          {/* For Influencers */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Za Influencere</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/influencers/dashboard" className="text-gray-300 hover:text-white transition-colors">
                  Dashboard
                </Link>
              </li>
              <li>
                <Link href="/influencers/profile" className="text-gray-300 hover:text-white transition-colors">
                  Moj Profil
                </Link>
              </li>
              <li>
                <Link href="/influencers/opportunities" className="text-gray-300 hover:text-white transition-colors">
                  Prilike
                </Link>
              </li>
              <li>
                <Link href="/influencers/earnings" className="text-gray-300 hover:text-white transition-colors">
                  Zarade
                </Link>
              </li>
              <li>
                <Link href="/influencers/resources" className="text-gray-300 hover:text-white transition-colors">
                  Resursi
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Kontakt</h3>
            <ul className="space-y-3 text-sm">
              <li className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-gray-400" />
                <span className="text-gray-300"><EMAIL></span>
              </li>
              <li className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-400" />
                <span className="text-gray-300">+387 33 123 456</span>
              </li>
              <li className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-gray-400" />
                <span className="text-gray-300">Sarajevo, BiH</span>
              </li>
            </ul>
            
            <div className="space-y-2">
              <h4 className="font-medium">Podrška</h4>
              <ul className="space-y-1 text-sm">
                <li>
                  <Link href="/help" className="text-gray-300 hover:text-white transition-colors">
                    Pomoć
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="text-gray-300 hover:text-white transition-colors">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-gray-300 hover:text-white transition-colors">
                    Kontaktiraj nas
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-400">
              © 2024 Collabstr BA. Sva prava zadržana.
            </div>
            <div className="flex space-x-6 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Privatnost
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                Uslovi korišćenja
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
