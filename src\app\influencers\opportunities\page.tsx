"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Search, 
  Filter, 
  Calendar,
  DollarSign,
  Users,
  Clock,
  MapPin,
  Building,
  Eye,
  Send,
  Heart,
  Bookmark,
  TrendingUp
} from "lucide-react";

const mockOpportunities = [
  {
    id: 1,
    title: "Ljetnja Kolekcija 2024",
    brand: "Fashion Nova BA",
    brandLogo: "/logos/fashion-nova.jpg",
    description: "Tražimo fashion influencere za promociju naše nove ljetnje kolekcije. Potrebni su kreativni postovi koji prikazuju različite outfite iz kolekcije.",
    category: "Fashion & Lifestyle",
    budget: "€150 - €300",
    deadline: "31. Jul 2024",
    requirements: [
      "Minimum 50K pratilaca na Instagramu",
      "Engagement rate preko 3%",
      "Lokacija: Sarajevo ili okolina",
      "1 Instagram post + 2 stories"
    ],
    contentType: ["Instagram Post", "Instagram Story"],
    location: "Sarajevo, BiH",
    applicants: 12,
    status: "Otvorena",
    postedDate: "5. Jul 2024",
    tags: ["fashion", "summer", "outfit", "style"]
  },
  {
    id: 2,
    title: "Fitness Challenge Kampanja",
    brand: "Fitness Center Sarajevo",
    brandLogo: "/logos/fitness-center.jpg",
    description: "30-dnevni fitness challenge sa našim trenerima. Tražimo fitness influencere koji će dokumentovati svoj put i motivirati svoju zajednicu.",
    category: "Fitness & Health",
    budget: "€200 - €400",
    deadline: "15. Avg 2024",
    requirements: [
      "Minimum 30K pratilaca",
      "Fokus na fitness sadržaj",
      "Dostupnost za 30 dana",
      "Instagram Reels + TikTok videi"
    ],
    contentType: ["Instagram Reel", "TikTok Video", "Instagram Story"],
    location: "Sarajevo, BiH",
    applicants: 8,
    status: "Otvorena",
    postedDate: "3. Jul 2024",
    tags: ["fitness", "challenge", "motivation", "health"]
  },
  {
    id: 3,
    title: "Tech Product Review",
    brand: "TechStore BiH",
    brandLogo: "/logos/techstore.jpg",
    description: "Recenzija najnovijeg smartphona. Tražimo tech influencere za detaljnu recenziju proizvoda sa fokusom na kameru i performanse.",
    category: "Technology",
    budget: "€300 - €500",
    deadline: "25. Jul 2024",
    requirements: [
      "Iskustvo sa tech recenzijama",
      "YouTube kanal ili Instagram",
      "Minimum 25K pratilaca",
      "Profesionalna kvaliteta videa"
    ],
    contentType: ["YouTube Video", "Instagram Post"],
    location: "Bilo koja lokacija u BiH",
    applicants: 5,
    status: "Otvorena",
    postedDate: "1. Jul 2024",
    tags: ["tech", "smartphone", "review", "gadgets"]
  },
  {
    id: 4,
    title: "Beauty Brand Collaboration",
    brand: "Glow Beauty BA",
    brandLogo: "/logos/glow-beauty.jpg",
    description: "Promocija nove linije prirodnih proizvoda za njegu kože. Tražimo beauty influencere za autentične recenzije i tutorials.",
    category: "Beauty & Skincare",
    budget: "€120 - €250",
    deadline: "20. Jul 2024",
    requirements: [
      "Beauty content creator",
      "Minimum 40K pratilaca",
      "Iskustvo sa skincare sadržajem",
      "Instagram + TikTok"
    ],
    contentType: ["Instagram Post", "TikTok Video", "Instagram Story"],
    location: "Mostar, BiH",
    applicants: 15,
    status: "Skoro zatvorena",
    postedDate: "28. Jun 2024",
    tags: ["beauty", "skincare", "natural", "tutorial"]
  },
  {
    id: 5,
    title: "Food Festival Promotion",
    brand: "Sarajevo Food Festival",
    brandLogo: "/logos/food-festival.jpg",
    description: "Promocija godišnjeg food festivala. Tražimo food blogere za coverage događaja i predstavljanje lokalnih restorana.",
    category: "Food & Travel",
    budget: "€180 - €350",
    deadline: "10. Avg 2024",
    requirements: [
      "Food blogger ili travel influencer",
      "Lokacija: Sarajevo",
      "Dostupnost tokom festivala (12-14. Avg)",
      "Instagram + TikTok sadržaj"
    ],
    contentType: ["Instagram Post", "Instagram Story", "TikTok Video"],
    location: "Sarajevo, BiH",
    applicants: 9,
    status: "Otvorena",
    postedDate: "30. Jun 2024",
    tags: ["food", "festival", "local", "restaurants"]
  },
  {
    id: 6,
    title: "Gaming Setup Showcase",
    brand: "Gaming Zone BA",
    brandLogo: "/logos/gaming-zone.jpg",
    description: "Predstavljanje gaming setup-a i najnovijih gaming periferija. Tražimo gaming influencere za unboxing i gameplay videe.",
    category: "Gaming",
    budget: "€250 - €450",
    deadline: "5. Avg 2024",
    requirements: [
      "Gaming content creator",
      "YouTube kanal obavezno",
      "Minimum 20K pretplatnika",
      "Kvalitetna produkcija videa"
    ],
    contentType: ["YouTube Video", "YouTube Shorts", "TikTok Video"],
    location: "Bilo koja lokacija",
    applicants: 7,
    status: "Otvorena",
    postedDate: "2. Jul 2024",
    tags: ["gaming", "setup", "unboxing", "peripherals"]
  }
];

export default function OpportunitiesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedBudget, setSelectedBudget] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Otvorena":
        return "default";
      case "Skoro zatvorena":
        return "destructive";
      case "Zatvorena":
        return "secondary";
      default:
        return "outline";
    }
  };

  const getBudgetRange = (budget: string) => {
    const match = budget.match(/€(\d+)\s*-\s*€(\d+)/);
    if (match) {
      const min = parseInt(match[1]);
      const max = parseInt(match[2]);
      return { min, max, display: budget };
    }
    return { min: 0, max: 0, display: budget };
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Prilike</h1>
        <p className="text-gray-600">Pronađite savršene kampanje za vašu nišu i auditorijum</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Eye className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Dostupne Prilike</p>
                <p className="text-xl font-bold">24</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Send className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Moje Aplikacije</p>
                <p className="text-xl font-bold">8</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Na čekanju</p>
                <p className="text-xl font-bold">3</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Uspješnost</p>
                <p className="text-xl font-bold">85%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filteri
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Pretraži</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Naziv kampanje ili brend"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Kategorija</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Sve kategorije" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Sve kategorije</SelectItem>
                  <SelectItem value="fashion">Fashion & Lifestyle</SelectItem>
                  <SelectItem value="beauty">Beauty & Skincare</SelectItem>
                  <SelectItem value="fitness">Fitness & Health</SelectItem>
                  <SelectItem value="food">Food & Travel</SelectItem>
                  <SelectItem value="tech">Technology</SelectItem>
                  <SelectItem value="gaming">Gaming</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Budžet</Label>
              <Select value={selectedBudget} onValueChange={setSelectedBudget}>
                <SelectTrigger>
                  <SelectValue placeholder="Svi budžeti" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Svi budžeti</SelectItem>
                  <SelectItem value="0-100">€0 - €100</SelectItem>
                  <SelectItem value="100-200">€100 - €200</SelectItem>
                  <SelectItem value="200-300">€200 - €300</SelectItem>
                  <SelectItem value="300-500">€300 - €500</SelectItem>
                  <SelectItem value="500+">€500+</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Svi statusi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Svi statusi</SelectItem>
                  <SelectItem value="otvorena">Otvorena</SelectItem>
                  <SelectItem value="skoro-zatvorena">Skoro zatvorena</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Opportunities List */}
      <div className="space-y-6">
        {mockOpportunities.map((opportunity) => (
          <Card key={opportunity.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Building className="h-6 w-6 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-lg font-semibold">{opportunity.title}</h3>
                      <Badge variant={getStatusColor(opportunity.status)}>
                        {opportunity.status}
                      </Badge>
                    </div>
                    <p className="text-gray-600 font-medium">{opportunity.brand}</p>
                    <p className="text-sm text-gray-500">Objavljeno {opportunity.postedDate}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="icon">
                    <Bookmark className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <p className="text-gray-700">{opportunity.description}</p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">Budžet:</span>
                  <span className="font-medium text-green-600">{opportunity.budget}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">Rok:</span>
                  <span className="font-medium">{opportunity.deadline}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">Aplikanti:</span>
                  <span className="font-medium">{opportunity.applicants}</span>
                </div>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">Lokacija:</span>
                <span className="font-medium">{opportunity.location}</span>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">Tip sadržaja:</h4>
                <div className="flex flex-wrap gap-2">
                  {opportunity.contentType.map((type) => (
                    <Badge key={type} variant="outline" className="text-xs">
                      {type}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">Zahtjevi:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {opportunity.requirements.map((req, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span>{req}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">Tagovi:</h4>
                <div className="flex flex-wrap gap-1">
                  {opportunity.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex gap-2 pt-4 border-t">
                <Button className="flex-1">
                  <Send className="h-4 w-4 mr-2" />
                  Apliciruj
                </Button>
                <Button variant="outline" className="flex-1">
                  <Eye className="h-4 w-4 mr-2" />
                  Više Detalja
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
